import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  GstValueService,
  GstValue,
} from '../../../../core/services/gst-value.service';

@Component({
  selector: 'app-gst-value-form',
  templateUrl: './gst-value-form.component.html',
  styleUrls: ['./gst-value-form.component.scss'],
})
export class GstValueFormComponent implements OnInit {
  gstValueForm: FormGroup;
  isEditMode = false;
  gstValueId: number | null = null;
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private gstValueService: GstValueService,
    private snackBar: MatSnackBar
  ) {
    this.gstValueForm = this.createForm();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.gstValueId = +params['id'];
        this.loadGstValue();
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group({
      value: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.max(100),
          Validators.pattern(/^\d+(\.\d{1,2})?$/),
        ],
      ],
      is_active: [true],
    });
  }

  loadGstValue(): void {
    if (!this.gstValueId) return;

    this.loading = true;
    this.gstValueService.getGstValueById(this.gstValueId).subscribe({
      next: (response) => {
        if (response.success) {
          this.populateForm(response.data);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading GST value:', error);
        this.showSnackBar('Failed to load GST value', true);
        this.loading = false;
        this.router.navigate(['/masters/gst-values']);
      },
    });
  }

  populateForm(gstValue: GstValue): void {
    this.gstValueForm.patchValue({
      value: gstValue.value,
      is_active: gstValue.is_active,
    });
  }

  onSubmit(): void {
    if (this.gstValueForm.valid) {
      this.submitting = true;
      const formData = this.gstValueForm.value;

      const operation = this.isEditMode
        ? this.gstValueService.updateGstValue(this.gstValueId!, formData)
        : this.gstValueService.createGstValue(formData);

      operation.subscribe({
        next: (response) => {
          if (response.success) {
            const message = this.isEditMode
              ? 'GST value updated successfully'
              : 'GST value created successfully';
            this.showSnackBar(message);
            this.router.navigate(['/masters/gst-values']);
          }
          this.submitting = false;
        },
        error: (error) => {
          console.error('Error saving GST value:', error);
          let errorMessage = 'Failed to save GST value';

          if (error.error?.message) {
            errorMessage = error.error.message;
          }

          this.showSnackBar(errorMessage, true);
          this.submitting = false;
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  markFormGroupTouched(): void {
    Object.keys(this.gstValueForm.controls).forEach((key) => {
      const control = this.gstValueForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.router.navigate(['/masters/gst-values']);
  }

  // Getter methods for form validation
  get value() {
    return this.gstValueForm.get('value');
  }
  get is_active() {
    return this.gstValueForm.get('is_active');
  }

  // Validation helper methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.gstValueForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.gstValueForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} must be at least ${
          field.errors['min'].min
        }`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} must be at most ${
          field.errors['max'].max
        }`;
      }
      if (field.errors['pattern']) {
        return `${this.getFieldLabel(
          fieldName
        )} must be a valid number with up to 2 decimal places`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      value: 'GST Value',
    };
    return labels[fieldName] || fieldName;
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
