.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.detail-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;

  mat-card-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;

    button {
      min-width: auto;
    }
  }
}

.loading-shade {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
  background-color: rgba(255, 255, 255, 0.8);
}

.error-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 40px 24px;
  text-align: center;
  color: #f44336;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
  }

  .error-content {
    flex: 1;

    h3 {
      margin: 0 0 8px 0;
      color: #f44336;
    }

    p {
      margin: 0 0 16px 0;
      color: #666;
    }
  }
}

mat-card-content {
  padding: 24px;

  .detail-section {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .detail-row {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-weight: 600;
        color: #555;
        min-width: 180px;
        flex-shrink: 0;
      }

      .detail-value {
        flex: 1;
        color: #333;

        &.brand-name {
          font-size: 18px;
          font-weight: 600;
          color: #1976d2;
        }
      }
    }
  }
}

// Chip styles
.subcategory-chip {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

.category-chip {
  background-color: #f3e5f5;
  color: #7b1fa2;
  font-weight: 500;
}

.active-chip {
  background-color: #e8f5e8;
  color: #2e7d32;
  font-weight: 500;

  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.inactive-chip {
  background-color: #ffebee;
  color: #c62828;
  font-weight: 500;

  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

// Info card styles
.info-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  mat-card-header {
    background-color: #e3f2fd;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      color: #1976d2;

      mat-icon {
        color: #1976d2;
      }
    }
  }

  mat-card-content {
    .info-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h6 {
        margin: 0 0 12px 0;
        color: #333;
        font-weight: 600;
        font-size: 16px;
      }

      .info-list {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: #555;
          line-height: 1.5;

          strong {
            color: #1976d2;
            font-weight: 600;
          }
        }
      }

      .quick-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        button {
          min-width: 160px;
        }
      }
    }
  }
}

mat-divider {
  margin: 16px 0;
  border-color: #e0e0e0;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  mat-card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;

    .header-actions {
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  mat-card-content {
    padding: 16px;

    .detail-section {
      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;

        .detail-label {
          min-width: auto;
          font-size: 14px;
        }

        .detail-value {
          font-size: 16px;
        }
      }
    }
  }

  .info-card {
    mat-card-content {
      .info-section {
        .quick-actions {
          flex-direction: column;

          button {
            width: 100%;
            min-width: auto;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  mat-card-header {
    .header-actions {
      button {
        flex: 1;
        min-width: auto;
      }
    }
  }
}
