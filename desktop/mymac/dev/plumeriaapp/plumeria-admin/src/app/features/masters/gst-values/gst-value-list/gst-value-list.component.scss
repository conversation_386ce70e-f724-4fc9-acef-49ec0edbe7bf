.gst-value-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .page-title {
      margin: 0;
      color: #333;
      font-weight: 600;
    }
  }
}

.filters-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .search-box {
    position: relative;
    
    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      z-index: 2;
    }
    
    .form-control {
      padding-left: 40px;
      border: 1px solid #ddd;
      border-radius: 6px;
      
      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
  }
  
  .filter-options {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    
    .form-check {
      margin: 0;
      
      .form-check-label {
        font-weight: 500;
        color: #495057;
      }
    }
  }
}

.bulk-actions {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 6px;
  padding: 12px 20px;
  margin-bottom: 20px;
  
  .bulk-actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .selected-count {
      font-weight: 500;
      color: #1976d2;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .table {
    margin: 0;
    
    th {
      background-color: #343a40 !important;
      color: white;
      font-weight: 600;
      border: none;
      padding: 15px 12px;
      
      &:first-child {
        padding-left: 20px;
      }
      
      &:last-child {
        padding-right: 20px;
      }
    }
    
    td {
      padding: 12px;
      vertical-align: middle;
      border-top: 1px solid #dee2e6;
      
      &:first-child {
        padding-left: 20px;
      }
      
      &:last-child {
        padding-right: 20px;
      }
    }
    
    tbody tr {
      &:hover {
        background-color: #f8f9fa;
      }
      
      &.table-secondary {
        background-color: #f8f9fa;
        opacity: 0.8;
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 5px;
    
    .btn {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      
      i {
        font-size: 12px;
      }
    }
  }
  
  .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    
    &.badge-success {
      background-color: #28a745;
    }
    
    &.badge-secondary {
      background-color: #6c757d;
    }
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  
  .no-data-content {
    max-width: 400px;
    margin: 0 auto;
    
    i {
      margin-bottom: 20px;
    }
    
    h4 {
      color: #495057;
      margin-bottom: 10px;
    }
    
    p {
      margin-bottom: 20px;
      line-height: 1.5;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .gst-value-list-container {
    padding: 15px;
  }
  
  .page-header .header-content {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    
    .page-title {
      text-align: center;
    }
  }
  
  .filters-section {
    .row {
      .col-md-6 {
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .filter-options {
      justify-content: flex-start;
    }
  }
  
  .bulk-actions .bulk-actions-content {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
    
    .selected-count {
      text-align: center;
    }
  }
  
  .table-container {
    overflow-x: auto;
    
    .table {
      min-width: 800px;
    }
  }
}
