import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  GstValueService,
  GstValue,
} from '../../../../core/services/gst-value.service';

@Component({
  selector: 'app-gst-value-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './gst-value-list.component.html',
  styleUrls: ['./gst-value-list.component.scss'],
})
export class GstValueListComponent implements OnInit {
  gstValues: GstValue[] = [];
  filteredGstValues: GstValue[] = [];
  loading = false;
  searchTerm = '';
  includeInactive = false;
  selectedGstValues: number[] = [];
  showBulkActions = false;

  constructor(
    private gstValueService: GstValueService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadGstValues();
  }

  loadGstValues(): void {
    this.loading = true;
    this.gstValueService.getGstValues(this.includeInactive).subscribe({
      next: (response) => {
        if (response.success) {
          this.gstValues = response.data;
          this.applyFilters();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading GST values:', error);
        this.showSnackBar('Failed to load GST values', true);
        this.loading = false;
      },
    });
  }

  applyFilters(): void {
    this.filteredGstValues = this.gstValues.filter((gstValue) => {
      const matchesSearch =
        this.searchTerm === '' ||
        gstValue.value.toString().includes(this.searchTerm) ||
        (gstValue.created_by_username &&
          gstValue.created_by_username
            .toLowerCase()
            .includes(this.searchTerm.toLowerCase()));

      return matchesSearch;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onIncludeInactiveChange(): void {
    this.loadGstValues();
  }

  navigateToCreate(): void {
    this.router.navigate(['/masters/gst-values/new']);
  }

  navigateToEdit(id: number): void {
    this.router.navigate(['/masters/gst-values/edit', id]);
  }

  navigateToView(id: number): void {
    this.router.navigate(['/masters/gst-values', id]);
  }

  toggleGstValueStatus(gstValue: GstValue): void {
    const newStatus = !gstValue.is_active;
    this.gstValueService
      .toggleGstValueStatus(gstValue.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            gstValue.is_active = newStatus;
            this.showSnackBar(
              `GST value ${
                newStatus ? 'activated' : 'deactivated'
              } successfully`
            );
          }
        },
        error: (error) => {
          console.error('Error toggling GST value status:', error);
          this.showSnackBar('Failed to update GST value status', true);
        },
      });
  }

  deleteGstValue(gstValue: GstValue): void {
    if (
      confirm(`Are you sure you want to delete GST value ${gstValue.value}%?`)
    ) {
      this.gstValueService.deleteGstValue(gstValue.id).subscribe({
        next: (response) => {
          this.showSnackBar('GST value deleted successfully');
          this.loadGstValues();
        },
        error: (error) => {
          console.error('Error deleting GST value:', error);
          this.showSnackBar('Failed to delete GST value', true);
        },
      });
    }
  }

  onGstValueSelect(gstValueId: number, event: any): void {
    if (event.target.checked) {
      this.selectedGstValues.push(gstValueId);
    } else {
      this.selectedGstValues = this.selectedGstValues.filter(
        (id) => id !== gstValueId
      );
    }
    this.showBulkActions = this.selectedGstValues.length > 0;
  }

  selectAllGstValues(event: any): void {
    if (event.target.checked) {
      this.selectedGstValues = this.filteredGstValues.map((gv) => gv.id);
    } else {
      this.selectedGstValues = [];
    }
    this.showBulkActions = this.selectedGstValues.length > 0;
  }

  bulkDeleteGstValues(): void {
    if (this.selectedGstValues.length === 0) return;

    const confirmMessage = `Are you sure you want to delete ${this.selectedGstValues.length} GST value(s)?`;
    if (confirm(confirmMessage)) {
      this.gstValueService
        .bulkDeleteGstValues(this.selectedGstValues)
        .subscribe({
          next: (response) => {
            this.showSnackBar(
              `${this.selectedGstValues.length} GST value(s) deleted successfully`
            );
            this.selectedGstValues = [];
            this.showBulkActions = false;
            this.loadGstValues();
          },
          error: (error) => {
            console.error('Error in bulk delete:', error);
            this.showSnackBar('Failed to delete GST values', true);
          },
        });
    }
  }

  isAllSelected(): boolean {
    return (
      this.filteredGstValues.length > 0 &&
      this.selectedGstValues.length === this.filteredGstValues.length
    );
  }

  isIndeterminate(): boolean {
    return (
      this.selectedGstValues.length > 0 &&
      this.selectedGstValues.length < this.filteredGstValues.length
    );
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
