import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  GstValueService,
  GstValue,
} from '../../../../core/services/gst-value.service';

@Component({
  selector: 'app-gst-value-detail',
  templateUrl: './gst-value-detail.component.html',
  styleUrls: ['./gst-value-detail.component.scss'],
})
export class GstValueDetailComponent implements OnInit {
  gstValue: GstValue | null = null;
  loading = false;
  gstValueId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gstValueService: GstValueService,
    private snackBar: MatSnackBar
  ) {
    this.gstValueId = +this.route.snapshot.params['id'];
  }

  ngOnInit(): void {
    this.loadGstValue();
  }

  loadGstValue(): void {
    this.loading = true;
    this.gstValueService.getGstValueById(this.gstValueId).subscribe({
      next: (response) => {
        if (response.success) {
          this.gstValue = response.data;
        } else {
          this.showSnackBar('GST value not found', true);
          this.router.navigate(['/masters/gst-values']);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading GST value:', error);
        this.showSnackBar('Failed to load GST value', true);
        this.router.navigate(['/masters/gst-values']);
        this.loading = false;
      },
    });
  }

  navigateToEdit(): void {
    this.router.navigate(['/masters/gst-values/edit', this.gstValueId]);
  }

  navigateToList(): void {
    this.router.navigate(['/masters/gst-values']);
  }

  toggleStatus(): void {
    if (!this.gstValue) return;

    const newStatus = !this.gstValue.is_active;
    this.gstValueService
      .toggleGstValueStatus(this.gstValue.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success && this.gstValue) {
            this.gstValue.is_active = newStatus;
            this.showSnackBar(
              `GST value ${
                newStatus ? 'activated' : 'deactivated'
              } successfully`
            );
          }
        },
        error: (error) => {
          console.error('Error toggling GST value status:', error);
          this.showSnackBar('Failed to update GST value status', true);
        },
      });
  }

  deleteGstValue(): void {
    if (!this.gstValue) return;

    const confirmMessage = `Are you sure you want to delete GST value ${this.gstValue.value}%?`;
    if (confirm(confirmMessage)) {
      this.gstValueService.deleteGstValue(this.gstValue.id).subscribe({
        next: (response) => {
          this.showSnackBar('GST value deleted successfully');
          this.router.navigate(['/masters/gst-values']);
        },
        error: (error) => {
          console.error('Error deleting GST value:', error);
          this.showSnackBar('Failed to delete GST value', true);
        },
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
