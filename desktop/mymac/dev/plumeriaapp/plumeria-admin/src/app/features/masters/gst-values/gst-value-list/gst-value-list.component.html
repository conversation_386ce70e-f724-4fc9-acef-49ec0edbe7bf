<div class="gst-value-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">GST Values</h1>
      <button 
        class="btn btn-primary"
        (click)="navigateToCreate()"
        [disabled]="loading">
        <i class="fas fa-plus"></i>
        Add GST Value
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="row">
      <div class="col-md-6">
        <div class="search-box">
          <i class="fas fa-search search-icon"></i>
          <input
            type="text"
            class="form-control"
            placeholder="Search GST values..."
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()">
        </div>
      </div>
      <div class="col-md-6">
        <div class="filter-options">
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="includeInactive"
              [(ngModel)]="includeInactive"
              (change)="onIncludeInactiveChange()">
            <label class="form-check-label" for="includeInactive">
              Include Inactive
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bulk Actions -->
  <div class="bulk-actions" *ngIf="showBulkActions">
    <div class="bulk-actions-content">
      <span class="selected-count">{{selectedGstValues.length}} item(s) selected</span>
      <button 
        class="btn btn-danger btn-sm"
        (click)="bulkDeleteGstValues()">
        <i class="fas fa-trash"></i>
        Delete Selected
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <!-- GST Values Table -->
  <div class="table-container" *ngIf="!loading">
    <table class="table table-striped table-hover">
      <thead class="table-dark">
        <tr>
          <th style="width: 50px;">
            <input
              type="checkbox"
              class="form-check-input"
              [checked]="isAllSelected()"
              [indeterminate]="isIndeterminate()"
              (change)="selectAllGstValues($event)">
          </th>
          <th>GST Value (%)</th>
          <th>Status</th>
          <th>Created By</th>
          <th>Created At</th>
          <th>Updated By</th>
          <th>Updated At</th>
          <th style="width: 150px;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let gstValue of filteredGstValues" 
            [class.table-secondary]="!gstValue.is_active">
          <td>
            <input
              type="checkbox"
              class="form-check-input"
              [checked]="selectedGstValues.includes(gstValue.id)"
              (change)="onGstValueSelect(gstValue.id, $event)">
          </td>
          <td>
            <strong>{{gstValue.value}}%</strong>
          </td>
          <td>
            <span class="badge" 
                  [class.badge-success]="gstValue.is_active"
                  [class.badge-secondary]="!gstValue.is_active">
              {{gstValue.is_active ? 'Active' : 'Inactive'}}
            </span>
          </td>
          <td>{{gstValue.created_by_username || 'N/A'}}</td>
          <td>{{gstValue.created_at | date:'short'}}</td>
          <td>{{gstValue.updated_by_username || 'N/A'}}</td>
          <td>{{gstValue.updated_at | date:'short'}}</td>
          <td>
            <div class="action-buttons">
              <button
                class="btn btn-sm btn-outline-primary"
                (click)="navigateToView(gstValue.id)"
                title="View">
                <i class="fas fa-eye"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-secondary"
                (click)="navigateToEdit(gstValue.id)"
                title="Edit">
                <i class="fas fa-edit"></i>
              </button>
              <button
                class="btn btn-sm"
                [class.btn-outline-success]="!gstValue.is_active"
                [class.btn-outline-warning]="gstValue.is_active"
                (click)="toggleGstValueStatus(gstValue)"
                [title]="gstValue.is_active ? 'Deactivate' : 'Activate'">
                <i class="fas" 
                   [class.fa-check]="!gstValue.is_active"
                   [class.fa-ban]="gstValue.is_active"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-danger"
                (click)="deleteGstValue(gstValue)"
                title="Delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- No Data Message -->
    <div class="no-data" *ngIf="filteredGstValues.length === 0">
      <div class="no-data-content">
        <i class="fas fa-percentage fa-3x text-muted"></i>
        <h4>No GST Values Found</h4>
        <p class="text-muted">
          <span *ngIf="searchTerm">No GST values match your search criteria.</span>
          <span *ngIf="!searchTerm">No GST values have been created yet.</span>
        </p>
        <button 
          class="btn btn-primary"
          (click)="navigateToCreate()"
          *ngIf="!searchTerm">
          <i class="fas fa-plus"></i>
          Add First GST Value
        </button>
      </div>
    </div>
  </div>
</div>
