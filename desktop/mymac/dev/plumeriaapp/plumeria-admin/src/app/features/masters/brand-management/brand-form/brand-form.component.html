<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} Brand</mat-card-title>
    </mat-card-header>

    <div class="loading-shade" *ngIf="loading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
    </div>

    <mat-card-content *ngIf="!loading">
      <form [formGroup]="brandForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Brand Name</mat-label>
            <input matInput
                   formControlName="name"
                   placeholder="Enter brand name (e.g., Chettinad, UltraTech, Havells)">
            <mat-error *ngIf="hasFieldError('name')">
              {{ getFieldError('name') }}
            </mat-error>
            <mat-hint>Enter the brand name as it appears on products</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Product Subcategory</mat-label>
            <mat-select formControlName="product_subcategory_id"
                        [disabled]="loadingSubcategories">
              <mat-option *ngIf="loadingSubcategories" disabled>
                Loading subcategories...
              </mat-option>
              <mat-option *ngFor="let subcategory of productSubcategories"
                          [value]="subcategory.id">
                {{ subcategory.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="hasFieldError('product_subcategory_id')">
              {{ getFieldError('product_subcategory_id') }}
            </mat-error>
            <mat-hint>Select the product subcategory this brand belongs to</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            Active
          </mat-slide-toggle>
          <span class="toggle-hint">
            {{ brandForm.get('is_active')?.value ? 'Brand is active and available for use' : 'Brand is inactive and hidden from selection' }}
          </span>
        </div>

        <div class="form-actions">
          <button mat-raised-button
                  type="button"
                  (click)="onCancel()"
                  [disabled]="submitting">
            Cancel
          </button>
          <button mat-raised-button
                  color="primary"
                  type="submit"
                  [disabled]="brandForm.invalid || submitting">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }} Brand</span>
            <span *ngIf="submitting">{{ isEditMode ? 'Updating...' : 'Creating...' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Help Section -->
  <mat-card class="help-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>help_outline</mat-icon>
        Brand Examples & Guidelines
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <h6>Common Construction Brands by Category:</h6>
      <div class="examples-grid">
        <div class="example-category">
          <h6>Cement Brands</h6>
          <ul class="help-list">
            <li><strong>Chettinad</strong> - Premium cement</li>
            <li><strong>UltraTech</strong> - Leading brand</li>
            <li><strong>ACC</strong> - Trusted quality</li>
            <li><strong>Ambuja</strong> - Reliable choice</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Electrical Brands</h6>
          <ul class="help-list">
            <li><strong>Havells</strong> - Switches & appliances</li>
            <li><strong>Anchor</strong> - Electrical accessories</li>
            <li><strong>Legrand</strong> - Premium solutions</li>
            <li><strong>Schneider</strong> - Industrial grade</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Plumbing Brands</h6>
          <ul class="help-list">
            <li><strong>Jaquar</strong> - Premium fittings</li>
            <li><strong>Kohler</strong> - Luxury products</li>
            <li><strong>Hindware</strong> - Sanitaryware</li>
            <li><strong>Cera</strong> - Bathroom solutions</li>
          </ul>
        </div>
        <div class="example-category">
          <h6>Steel Brands</h6>
          <ul class="help-list">
            <li><strong>TATA Steel</strong> - Premium quality</li>
            <li><strong>JSW Steel</strong> - Reliable choice</li>
            <li><strong>SAIL</strong> - Government enterprise</li>
            <li><strong>Jindal</strong> - Quality products</li>
          </ul>
        </div>
      </div>

      <div class="guidelines">
        <h6>Guidelines:</h6>
        <ul>
          <li>Use the exact brand name as it appears on products</li>
          <li>Select the most specific subcategory for the brand</li>
          <li>Each brand can only belong to one subcategory</li>
          <li>Brand names must be unique within each subcategory</li>
          <li>Inactive brands won't appear in product selection lists</li>
        </ul>
      </div>
    </mat-card-content>
  </mat-card>
</div>
