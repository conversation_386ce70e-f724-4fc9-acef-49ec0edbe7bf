<div class="gst-value-form-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-percentage"></i>
        {{isEditMode ? 'Edit GST Value' : 'Add New GST Value'}}
      </h1>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <a routerLink="/masters/gst-values">GST Values</a>
          </li>
          <li class="breadcrumb-item active">
            {{isEditMode ? 'Edit' : 'Add New'}}
          </li>
        </ol>
      </nav>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <!-- Form -->
  <div class="form-container" *ngIf="!loading">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-info-circle"></i>
          GST Value Information
        </h5>
      </div>
      <div class="card-body">
        <form [formGroup]="gstValueForm" (ngSubmit)="onSubmit()">
          <div class="row">
            <!-- GST Value -->
            <div class="col-md-6">
              <div class="form-group">
                <label for="value" class="form-label required">
                  <i class="fas fa-percentage"></i>
                  GST Value (%)
                </label>
                <input
                  type="number"
                  id="value"
                  class="form-control"
                  formControlName="value"
                  placeholder="Enter GST value (e.g., 18)"
                  min="0"
                  max="100"
                  step="0.01"
                  [class.is-invalid]="isFieldInvalid('value')">
                <div class="invalid-feedback" *ngIf="isFieldInvalid('value')">
                  {{getFieldError('value')}}
                </div>
                <small class="form-text text-muted">
                  Enter a value between 0 and 100 (up to 2 decimal places)
                </small>
              </div>
            </div>

            <!-- Status -->
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-toggle-on"></i>
                  Status
                </label>
                <div class="form-check-container">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="is_active"
                      formControlName="is_active">
                    <label class="form-check-label" for="is_active">
                      Active
                    </label>
                  </div>
                </div>
                <small class="form-text text-muted">
                  Inactive GST values will not be available for selection
                </small>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <div class="action-buttons">
              <button
                type="button"
                class="btn btn-secondary"
                (click)="onCancel()"
                [disabled]="submitting">
                <i class="fas fa-times"></i>
                Cancel
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="submitting || gstValueForm.invalid">
                <span *ngIf="submitting" class="spinner-border spinner-border-sm me-2"></span>
                <i *ngIf="!submitting" class="fas" [class.fa-save]="isEditMode" [class.fa-plus]="!isEditMode"></i>
                {{submitting ? 'Saving...' : (isEditMode ? 'Update GST Value' : 'Create GST Value')}}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Help Section -->
    <div class="help-section">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fas fa-question-circle"></i>
            Help & Guidelines
          </h6>
        </div>
        <div class="card-body">
          <div class="help-content">
            <h6>Common GST Rates in India:</h6>
            <ul class="help-list">
              <li><strong>0%</strong> - Essential items (basic food items, books, etc.)</li>
              <li><strong>5%</strong> - Basic necessities (sugar, tea, coffee, etc.)</li>
              <li><strong>12%</strong> - Standard rate (computers, processed food, etc.)</li>
              <li><strong>18%</strong> - Most goods and services</li>
              <li><strong>28%</strong> - Luxury items (cars, tobacco, etc.)</li>
            </ul>
            <div class="help-note">
              <i class="fas fa-info-circle"></i>
              <span>You can enter custom GST rates as needed for your business requirements.</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
