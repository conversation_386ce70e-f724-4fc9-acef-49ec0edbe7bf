.container {
  padding: 20px;
}

.form-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 20px auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.help-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  margin-bottom: 20px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  padding: 15px;
  margin-bottom: 20px;
  background-color: #ffebee;
  border-radius: 4px;
}

.error-message {
  color: #f44336;
  margin: 0;
}

.form-row {
  margin-bottom: 20px;

  mat-form-field {
    width: 100%;
  }
}

.status-toggle {
  margin-top: 10px;
  margin-bottom: 30px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;

  button {
    min-width: 100px;
  }
}

/* <PERSON><PERSON> with spinner */
button mat-spinner {
  display: inline-block;
  margin-right: 5px;
}

/* Help section styling */
.help-list {
  margin: 15px 0;
  padding-left: 20px;

  li {
    margin-bottom: 8px;

    strong {
      color: #1976d2;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-card, .help-card {
    max-width: 100%;
  }

  .help-list {
    padding-left: 15px;
  }
}
