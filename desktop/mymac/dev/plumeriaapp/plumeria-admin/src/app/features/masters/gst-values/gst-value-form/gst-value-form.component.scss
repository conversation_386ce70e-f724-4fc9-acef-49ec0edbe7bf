.gst-value-form-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  
  .header-content {
    .page-title {
      color: #333;
      font-weight: 600;
      margin-bottom: 10px;
      
      i {
        margin-right: 10px;
        color: #007bff;
      }
    }
    
    .breadcrumb {
      background: none;
      padding: 0;
      margin: 0;
      
      .breadcrumb-item {
        a {
          color: #007bff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        &.active {
          color: #6c757d;
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.form-container {
  .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
    
    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 15px 20px;
      
      .card-title {
        color: #495057;
        font-weight: 600;
        
        i {
          margin-right: 8px;
          color: #007bff;
        }
      }
    }
    
    .card-body {
      padding: 25px;
    }
  }
}

.form-group {
  margin-bottom: 20px;
  
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
    
    i {
      margin-right: 6px;
      color: #6c757d;
      width: 14px;
    }
    
    &.required::after {
      content: ' *';
      color: #dc3545;
    }
  }
  
  .form-control {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    &.is-invalid {
      border-color: #dc3545;
      
      &:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
      }
    }
  }
  
  .form-check-container {
    margin-top: 8px;
    
    .form-check {
      padding-left: 0;
      
      .form-check-input {
        margin-right: 8px;
        
        &:checked {
          background-color: #007bff;
          border-color: #007bff;
        }
        
        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
      
      .form-check-label {
        font-weight: 500;
        color: #495057;
      }
    }
  }
  
  .form-text {
    font-size: 12px;
    margin-top: 5px;
  }
  
  .invalid-feedback {
    font-size: 12px;
    margin-top: 5px;
  }
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  
  .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    
    .btn {
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 6px;
      
      i {
        margin-right: 6px;
      }
      
      &.btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        
        &:hover {
          background-color: #0056b3;
          border-color: #0056b3;
        }
        
        &:disabled {
          background-color: #6c757d;
          border-color: #6c757d;
        }
      }
      
      &.btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        
        &:hover {
          background-color: #545b62;
          border-color: #545b62;
        }
      }
    }
  }
}

.help-section {
  .card {
    .card-header {
      background-color: #e3f2fd;
      border-bottom: 1px solid #bbdefb;
      
      .card-title {
        color: #1976d2;
        
        i {
          color: #1976d2;
        }
      }
    }
    
    .card-body {
      padding: 20px;
    }
  }
  
  .help-content {
    h6 {
      color: #495057;
      font-weight: 600;
      margin-bottom: 12px;
    }
    
    .help-list {
      margin-bottom: 15px;
      padding-left: 20px;
      
      li {
        margin-bottom: 6px;
        color: #6c757d;
        
        strong {
          color: #495057;
        }
      }
    }
    
    .help-note {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 12px;
      display: flex;
      align-items: flex-start;
      
      i {
        color: #17a2b8;
        margin-right: 8px;
        margin-top: 2px;
        flex-shrink: 0;
      }
      
      span {
        color: #495057;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .gst-value-form-container {
    padding: 15px;
  }
  
  .form-container .card .card-body {
    padding: 20px 15px;
  }
  
  .form-actions .action-buttons {
    flex-direction: column;
    
    .btn {
      width: 100%;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .help-section .help-content .help-list {
    padding-left: 15px;
  }
}
