import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

import { GstValueListComponent } from './gst-value-list/gst-value-list.component';
import { GstValueFormComponent } from './gst-value-form/gst-value-form.component';
import { GstValueDetailComponent } from './gst-value-detail/gst-value-detail.component';

const routes: Routes = [
  {
    path: '',
    component: GstValueListComponent
  },
  {
    path: 'new',
    component: GstValueFormComponent
  },
  {
    path: 'edit/:id',
    component: GstValueFormComponent
  },
  {
    path: ':id',
    component: GstValueDetailComponent
  }
];

@NgModule({
  declarations: [
    GstValueListComponent,
    GstValueFormComponent,
    GstValueDetailComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule.forChild(routes)
  ]
})
export class GstValuesModule { }
