import { Routes } from '@angular/router';
import { CountryManagementComponent } from './country-management/country-management.component';
import { CountryListComponent } from './country-management/country-list/country-list.component';
import { CountryFormComponent } from './country-management/country-form/country-form.component';
import { CountryDetailComponent } from './country-management/country-detail/country-detail.component';
import { StateManagementComponent } from './state-management/state-management.component';
import { StateListComponent } from './state-management/state-list/state-list.component';
import { StateFormComponent } from './state-management/state-form/state-form.component';
import { StateDetailComponent } from './state-management/state-detail/state-detail.component';
import { CityManagementComponent } from './city-management/city-management.component';
import { CityListComponent } from './city-management/city-list/city-list.component';
import { CityFormComponent } from './city-management/city-form/city-form.component';
import { CityDetailComponent } from './city-management/city-detail/city-detail.component';
import { LocalityManagementComponent } from './locality-management/locality-management.component';
import { LocalityListComponent } from './locality-management/locality-list/locality-list.component';
import { LocalityFormComponent } from './locality-management/locality-form/locality-form.component';
import { LocalityDetailComponent } from './locality-management/locality-detail/locality-detail.component';
import { DepartmentManagementComponent } from './department-management/department-management.component';
import { DepartmentListComponent } from './department-management/department-list/department-list.component';
import { DepartmentFormComponent } from './department-management/department-form/department-form.component';
import { DepartmentDetailComponent } from './department-management/department-detail/department-detail.component';
import { DesignationManagementComponent } from './designation-management/designation-management.component';
import { DesignationListComponent } from './designation-management/designation-list/designation-list.component';
import { DesignationFormComponent } from './designation-management/designation-form/designation-form.component';
import { DesignationDetailComponent } from './designation-management/designation-detail/designation-detail.component';
import { BloodGroupManagementComponent } from './blood-group-management/blood-group-management.component';
import { BloodGroupListComponent } from './blood-group-management/blood-group-list/blood-group-list.component';
import { BloodGroupFormComponent } from './blood-group-management/blood-group-form/blood-group-form.component';
import { BloodGroupDetailComponent } from './blood-group-management/blood-group-detail/blood-group-detail.component';
import { EmploymentTypeManagementComponent } from './employment-type-management/employment-type-management.component';
import { EmploymentTypeListComponent } from './employment-type-management/employment-type-list/employment-type-list.component';
import { EmploymentTypeFormComponent } from './employment-type-management/employment-type-form/employment-type-form.component';
import { EmploymentTypeDetailComponent } from './employment-type-management/employment-type-detail/employment-type-detail.component';
import { ProjectTypeManagementComponent } from './project-type-management/project-type-management.component';
import { ProjectTypeListComponent } from './project-type-management/project-type-list/project-type-list.component';
import { ProjectTypeFormComponent } from './project-type-management/project-type-form/project-type-form.component';
import { ProjectTypeDetailComponent } from './project-type-management/project-type-detail/project-type-detail.component';
import { AuthGuard } from '../../core/guards/auth.guard';
import { PermissionGuard } from '../../core/guards/permission.guard';
import { QualificationManagementComponent } from './qualification/qualification-management/qualification-management.component';
import { QualificationListComponent } from './qualification/qualification-list/qualification-list.component';
import { QualificationFormComponent } from './qualification/qualification-form/qualification-form.component';
import { QualificationDetailComponent } from './qualification/qualification-detail/qualification-detail.component';
import { ProjectStatusManagementComponent } from './project-status/project-status-management/project-status-management.component';
import { ProjectStatusListComponent } from './project-status/project-status-list/project-status-list.component';
import { ProjectStatusFormComponent } from './project-status/project-status-form/project-status-form.component';
import { ProjectStatusDetailComponent } from './project-status/project-status-detail/project-status-detail.component';
import { SupplierTypeManagementComponent } from './supplier-type-management/supplier-type-management.component';
import { SupplierTypeListComponent } from './supplier-type-management/supplier-type-list/supplier-type-list.component';
import { SupplierTypeFormComponent } from './supplier-type-management/supplier-type-form/supplier-type-form.component';
import { SupplierTypeViewComponent } from './supplier-type-management/supplier-type-view/supplier-type-view.component';
import { ProductCategoryManagementComponent } from './product-category/product-category-management/product-category-management.component';
import { ProductCategoryListComponent } from './product-category/product-category-list/product-category-list.component';
import { ProductCategoryFormComponent } from './product-category/product-category-form/product-category-form.component';
import { ProductCategoryViewComponent } from './product-category/product-category-view/product-category-view.component';
import { ProductSubcategoryManagementComponent } from './product-subcategory/product-subcategory-management/product-subcategory-management.component';
import { ProductSubcategoryListComponent } from './product-subcategory/product-subcategory-list/product-subcategory-list.component';
import { ProductSubcategoryFormComponent } from './product-subcategory/product-subcategory-form/product-subcategory-form.component';
import { ProductSubcategoryViewComponent } from './product-subcategory/product-subcategory-view/product-subcategory-view.component';
import { GstValueManagementComponent } from './gst-values/gst-value-management.component';
import { GstValueListComponent } from './gst-values/gst-value-list/gst-value-list.component';
import { GstValueFormComponent } from './gst-values/gst-value-form/gst-value-form.component';
import { GstValueDetailComponent } from './gst-values/gst-value-detail/gst-value-detail.component';
import { MeasurementUnitManagementComponent } from './measurement-units/measurement-unit-management.component';
import { MeasurementUnitListComponent } from './measurement-units/measurement-unit-list/measurement-unit-list.component';
import { MeasurementUnitFormComponent } from './measurement-units/measurement-unit-form/measurement-unit-form.component';
import { MeasurementUnitDetailComponent } from './measurement-units/measurement-unit-detail/measurement-unit-detail.component';

export const MASTERS_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'countries',
    pathMatch: 'full',
  },
  // Countries routes
  {
    path: 'countries',
    component: CountryManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: CountryListComponent,
      },
      {
        path: 'new',
        component: CountryFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: CountryDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: CountryFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // States routes
  {
    path: 'states',
    component: StateManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: StateListComponent,
      },
      {
        path: 'new',
        component: StateFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: StateDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: StateFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Cities routes
  {
    path: 'cities',
    component: CityManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: CityListComponent,
      },
      {
        path: 'new',
        component: CityFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: CityDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: CityFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Localities routes
  {
    path: 'localities',
    component: LocalityManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: LocalityListComponent,
      },
      {
        path: 'new',
        component: LocalityFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: LocalityDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: LocalityFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Departments routes
  {
    path: 'departments',
    component: DepartmentManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: DepartmentListComponent,
      },
      {
        path: 'new',
        component: DepartmentFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: DepartmentDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: DepartmentFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Designations routes
  {
    path: 'designations',
    component: DesignationManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: DesignationListComponent,
      },
      {
        path: 'new',
        component: DesignationFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: DesignationDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: DesignationFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Blood Groups routes
  {
    path: 'blood-groups',
    component: BloodGroupManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: BloodGroupListComponent,
      },
      {
        path: 'new',
        component: BloodGroupFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: BloodGroupDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: BloodGroupFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Employment Types routes
  {
    path: 'employment-types',
    component: EmploymentTypeManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: EmploymentTypeListComponent,
      },
      {
        path: 'new',
        component: EmploymentTypeFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: EmploymentTypeDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: EmploymentTypeFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },
  // Project Types routes
  {
    path: 'project-types',
    component: ProjectTypeManagementComponent,
    canActivate: [AuthGuard], // Removed PermissionGuard for testing
    children: [
      {
        path: '',
        component: ProjectTypeListComponent,
      },
      {
        path: 'new',
        component: ProjectTypeFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: ':id',
        component: ProjectTypeDetailComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
      {
        path: 'edit/:id',
        component: ProjectTypeFormComponent,
        canActivate: [AuthGuard], // Removed PermissionGuard for testing
      },
    ],
  },

  {
    path: 'qualifications',
    component: QualificationManagementComponent,
    children: [
      { path: '', component: QualificationListComponent },
      { path: 'new', component: QualificationFormComponent },
      { path: ':id', component: QualificationDetailComponent },
      { path: 'edit/:id', component: QualificationFormComponent },
    ],
  },

  {
    path: 'project-statuses',
    component: ProjectStatusManagementComponent,
    children: [
      { path: '', component: ProjectStatusListComponent },
      { path: 'new', component: ProjectStatusFormComponent },
      { path: ':id', component: ProjectStatusDetailComponent },
      { path: 'edit/:id', component: ProjectStatusFormComponent },
    ],
  },

  // Supplier Types routes
  {
    path: 'supplier-types',
    component: SupplierTypeManagementComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: SupplierTypeListComponent },
      { path: 'new', component: SupplierTypeFormComponent },
      { path: ':id', component: SupplierTypeViewComponent },
      { path: 'edit/:id', component: SupplierTypeFormComponent },
    ],
  },

  // Product Categories routes
  {
    path: 'product-categories',
    component: ProductCategoryManagementComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: ProductCategoryListComponent },
      { path: 'new', component: ProductCategoryFormComponent },
      { path: ':id', component: ProductCategoryViewComponent },
      { path: 'edit/:id', component: ProductCategoryFormComponent },
    ],
  },

  // Product Subcategories routes
  {
    path: 'product-subcategories',
    component: ProductSubcategoryManagementComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: ProductSubcategoryListComponent },
      { path: 'new', component: ProductSubcategoryFormComponent },
      { path: ':id', component: ProductSubcategoryViewComponent },
      { path: 'edit/:id', component: ProductSubcategoryFormComponent },
    ],
  },

  // GST Values routes
  {
    path: 'gst-values',
    component: GstValueManagementComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: GstValueListComponent },
      { path: 'new', component: GstValueFormComponent },
      { path: ':id', component: GstValueDetailComponent },
      { path: 'edit/:id', component: GstValueFormComponent },
    ],
  },

  // Measurement Units routes
  {
    path: 'measurement-units',
    component: MeasurementUnitManagementComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: MeasurementUnitListComponent },
      { path: 'new', component: MeasurementUnitFormComponent },
      { path: ':id', component: MeasurementUnitDetailComponent },
      { path: 'edit/:id', component: MeasurementUnitFormComponent },
    ],
  },
];
