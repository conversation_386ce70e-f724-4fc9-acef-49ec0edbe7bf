import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  BrandService,
  Brand,
} from '../../../../core/services/masters/brand.service';

@Component({
  selector: 'app-brand-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDividerModule,
    MatTooltipModule,
  ],
  templateUrl: './brand-detail.component.html',
  styleUrls: ['./brand-detail.component.scss'],
})
export class BrandDetailComponent implements OnInit {
  brand: Brand | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private brandService: BrandService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadBrand();
  }

  loadBrand(): void {
    this.isLoading = true;
    this.errorMessage = '';

    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.errorMessage = 'Brand ID is missing';
      this.isLoading = false;
      return;
    }

    this.brandService.getBrandById(+id).subscribe({
      next: (brand) => {
        this.brand = brand;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load brand: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  editBrand(): void {
    if (this.brand) {
      this.router.navigate(['/masters/brands/edit', this.brand.id]);
    }
  }

  toggleBrandStatus(): void {
    if (!this.brand) return;

    this.brandService.toggleBrandStatus(this.brand.id!).subscribe({
      next: (updatedBrand) => {
        this.brand = updatedBrand;
        this.snackBar.open(
          `Brand ${
            updatedBrand.is_active ? 'activated' : 'deactivated'
          } successfully`,
          'Close',
          { duration: 3000 }
        );
      },
      error: (error) => {
        this.snackBar.open(
          `Failed to update brand status: ${error.message}`,
          'Close',
          {
            duration: 5000,
          }
        );
      },
    });
  }

  deleteBrand(): void {
    if (!this.brand) return;

    if (
      confirm(`Are you sure you want to delete the brand "${this.brand.name}"?`)
    ) {
      this.brandService.deleteBrand(this.brand.id!).subscribe({
        next: () => {
          this.snackBar.open('Brand deleted successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['/masters/brands']);
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to delete brand: ${error.message}`,
            'Close',
            {
              duration: 5000,
            }
          );
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/masters/brands']);
  }
}
