<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Brand Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editBrand()" matTooltip="Edit Brand" *ngIf="brand">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="accent" (click)="toggleBrandStatus()"
                matTooltip="{{ brand.is_active ? 'Deactivate' : 'Activate' }}" *ngIf="brand">
          <mat-icon>{{ brand.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          {{ brand.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteBrand()" matTooltip="Delete Brand" *ngIf="brand">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()" matTooltip="Back to List">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage && !isLoading">
      <mat-icon>error_outline</mat-icon>
      <div class="error-content">
        <h3>Error Loading Brand</h3>
        <p>{{ errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="loadBrand()">
          <mat-icon>refresh</mat-icon> Retry
        </button>
      </div>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage && brand">
      <div class="detail-section">
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ brand.id }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Brand Name:</div>
          <div class="detail-value brand-name">{{ brand.name }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Product Subcategory:</div>
          <div class="detail-value">
            <mat-chip class="subcategory-chip">{{ brand.product_subcategory_name }}</mat-chip>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Product Category:</div>
          <div class="detail-value">
            <mat-chip class="category-chip">{{ brand.product_category_name }}</mat-chip>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Status:</div>
          <div class="detail-value">
            <mat-chip [ngClass]="brand.is_active ? 'active-chip' : 'inactive-chip'">
              <mat-icon>{{ brand.is_active ? 'check_circle' : 'cancel' }}</mat-icon>
              {{ brand.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ brand.created_by_username || 'N/A' }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ brand.created_at | date:'medium' }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.updated_by_username">
          <div class="detail-label">Last Updated By:</div>
          <div class="detail-value">{{ brand.updated_by_username }}</div>
        </div>

        <div class="detail-row" *ngIf="brand.updated_at">
          <div class="detail-label">Last Updated At:</div>
          <div class="detail-value">{{ brand.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Related Information Card -->
  <mat-card class="info-card" *ngIf="brand">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>info</mat-icon>
        Brand Information
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="info-section">
        <h6>Usage Guidelines:</h6>
        <ul class="info-list">
          <li>This brand is categorized under <strong>{{ brand.product_subcategory_name }}</strong> products</li>
          <li>It belongs to the <strong>{{ brand.product_category_name }}</strong> category</li>
          <li *ngIf="brand.is_active">This brand is currently active and available for product selection</li>
          <li *ngIf="!brand.is_active">This brand is inactive and won't appear in product selection lists</li>
          <li>Brand names must be unique within each product subcategory</li>
        </ul>
      </div>

      <div class="info-section">
        <h6>Quick Actions:</h6>
        <div class="quick-actions">
          <button mat-stroked-button color="primary" (click)="editBrand()">
            <mat-icon>edit</mat-icon> Edit Brand Details
          </button>
          <button mat-stroked-button color="accent" (click)="toggleBrandStatus()">
            <mat-icon>{{ brand.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ brand.is_active ? 'Deactivate' : 'Activate' }} Brand
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
