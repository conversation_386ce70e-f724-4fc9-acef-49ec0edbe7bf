-- Create brands table for construction company
-- Simple brand master linked to product subcategories

CREATE TABLE brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    product_subcategory_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    updated_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_brands_product_subcategory 
        FOREIGN KEY (product_subcategory_id) 
        REFERENCES product_subcategories(id) 
        ON DELETE RESTRICT ON UPDATE CASCADE,
        
    CONSTRAINT fk_brands_created_by 
        FOR<PERSON><PERSON><PERSON> KEY (created_by) 
        REFERENCES users(id) 
        ON DELETE RESTRICT ON UPDATE CASCADE,
        
    CONSTRAINT fk_brands_updated_by 
        FOREIGN KEY (updated_by) 
        REFERENCES users(id) 
        ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_brands_name (name),
    INDEX idx_brands_subcategory (product_subcategory_id),
    INDEX idx_brands_active (is_active),
    
    -- Unique constraint to prevent duplicate brand names within same subcategory
    UNIQUE KEY uk_brands_name_subcategory (name, product_subcategory_id)
);

-- Sample brand data for construction industry
-- Note: Replace product_subcategory_id values with actual IDs from your product_subcategories table

INSERT INTO brands (name, product_subcategory_id, created_by) VALUES 
-- Cement brands (replace subcategory IDs with actual ones)
('Chettinad', 1, 1),
('UltraTech', 1, 1),
('ACC', 1, 1),
('Ambuja', 1, 1),

-- Steel brands
('TATA Steel', 2, 1),
('JSW Steel', 2, 1),
('SAIL', 2, 1),

-- Electrical brands
('Havells', 3, 1),
('Anchor', 3, 1),
('Legrand', 3, 1),
('Schneider Electric', 3, 1),

-- Plumbing brands
('Jaquar', 4, 1),
('Kohler', 4, 1),
('Hindware', 4, 1),
('Cera', 4, 1),

-- Paint brands
('Asian Paints', 5, 1),
('Berger Paints', 5, 1),
('Nerolac', 5, 1),

-- Tiles brands
('Kajaria', 6, 1),
('Somany', 6, 1),
('Nitco', 6, 1);

-- Add table comment
ALTER TABLE brands COMMENT = 'Master table for brand names linked to product subcategories';
