-- Create brands table for construction company
-- This table stores brand information linked to product subcategories

CREATE TABLE brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    product_subcategory_id INT NOT NULL,
    description TEXT,
    manufacturer_name VARCHA<PERSON>(150),
    country_of_origin VARCHAR(100),
    website_url VARCHAR(255),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    is_preferred BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a preferred brand for the company',
    quality_rating TINYINT DEFAULT NULL COMMENT 'Internal quality rating from 1-5',
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    updated_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_brands_product_subcategory 
        FOREIGN KEY (product_subcategory_id) 
        REFERENCES product_subcategories(id) 
        ON DELETE RESTRICT ON UPDATE CASCADE,
        
    CONSTRAINT fk_brands_created_by 
        FOREIGN KEY (created_by) 
        REFERENCES users(id) 
        ON DELETE RESTRICT ON UPDATE CASCADE,
        
    CONSTRAINT fk_brands_updated_by 
        FOREIGN KEY (updated_by) 
        REFERENCES users(id) 
        ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_brands_name (name),
    INDEX idx_brands_subcategory (product_subcategory_id),
    INDEX idx_brands_active (is_active),
    INDEX idx_brands_preferred (is_preferred),
    INDEX idx_brands_quality (quality_rating),
    
    -- Unique constraint to prevent duplicate brand names within same subcategory
    UNIQUE KEY uk_brands_name_subcategory (name, product_subcategory_id)
);

-- Insert sample brand data for construction industry
INSERT INTO brands (
    name, 
    product_subcategory_id, 
    description, 
    manufacturer_name, 
    country_of_origin, 
    website_url, 
    is_preferred, 
    quality_rating, 
    created_by
) VALUES 
-- Cement brands (assuming subcategory id 1 is cement)
('Chettinad', 1, 'Premium quality cement for construction', 'Chettinad Cement Corporation Limited', 'India', 'https://www.chettinadcement.com', TRUE, 5, 1),
('UltraTech', 1, 'Leading cement brand in India', 'UltraTech Cement Limited', 'India', 'https://www.ultratechcement.com', TRUE, 5, 1),
('ACC', 1, 'Trusted cement brand', 'ACC Limited', 'India', 'https://www.acclimited.com', TRUE, 4, 1),
('Ambuja', 1, 'Quality cement products', 'Ambuja Cements Limited', 'India', 'https://www.ambujacement.com', TRUE, 4, 1),

-- Steel brands (assuming subcategory id 2 is steel)
('TATA Steel', 2, 'Premium steel products', 'Tata Steel Limited', 'India', 'https://www.tatasteel.com', TRUE, 5, 1),
('JSW Steel', 2, 'Quality steel manufacturer', 'JSW Steel Limited', 'India', 'https://www.jswsteel.in', TRUE, 5, 1),
('SAIL', 2, 'Steel Authority of India', 'Steel Authority of India Limited', 'India', 'https://www.sail.co.in', TRUE, 4, 1),

-- Electrical brands (assuming subcategory id 3 is electrical)
('Havells', 3, 'Electrical equipment and appliances', 'Havells India Limited', 'India', 'https://www.havells.com', TRUE, 5, 1),
('Anchor', 3, 'Electrical switches and accessories', 'Anchor Electricals Pvt Ltd', 'India', 'https://www.anchorelectricals.com', TRUE, 4, 1),
('Legrand', 3, 'Global electrical solutions', 'Legrand Group', 'France', 'https://www.legrand.com', TRUE, 5, 1),
('Schneider Electric', 3, 'Energy management solutions', 'Schneider Electric SE', 'France', 'https://www.schneider-electric.com', TRUE, 5, 1),

-- Plumbing brands (assuming subcategory id 4 is plumbing)
('Jaquar', 4, 'Premium bathroom fittings', 'Jaquar Group', 'India', 'https://www.jaquar.com', TRUE, 5, 1),
('Kohler', 4, 'Luxury bathroom products', 'Kohler Co.', 'USA', 'https://www.kohler.com', TRUE, 5, 1),
('Hindware', 4, 'Sanitaryware and faucets', 'Hindware Limited', 'India', 'https://www.hindware.com', TRUE, 4, 1),
('Cera', 4, 'Sanitaryware products', 'Cera Sanitaryware Limited', 'India', 'https://www.cera.in', TRUE, 4, 1),

-- Paint brands (assuming subcategory id 5 is paints)
('Asian Paints', 5, 'Leading paint manufacturer', 'Asian Paints Limited', 'India', 'https://www.asianpaints.com', TRUE, 5, 1),
('Berger Paints', 5, 'Quality paint products', 'Berger Paints India Limited', 'India', 'https://www.bergerpaints.com', TRUE, 4, 1),
('Nerolac', 5, 'Decorative and industrial paints', 'Kansai Nerolac Paints Limited', 'India', 'https://www.nerolac.com', TRUE, 4, 1),
('Dulux', 5, 'Premium paint brand', 'AkzoNobel', 'Netherlands', 'https://www.dulux.com', TRUE, 5, 1),

-- Tiles brands (assuming subcategory id 6 is tiles)
('Kajaria', 6, 'Leading tiles manufacturer', 'Kajaria Ceramics Limited', 'India', 'https://www.kajariaceramics.com', TRUE, 5, 1),
('Somany', 6, 'Ceramic and vitrified tiles', 'Somany Ceramics Limited', 'India', 'https://www.somanyceramics.com', TRUE, 4, 1),
('Nitco', 6, 'Premium tiles and surfaces', 'Nitco Limited', 'India', 'https://www.nitco.in', TRUE, 4, 1),

-- Hardware brands (assuming subcategory id 7 is hardware)
('Godrej', 7, 'Locks and security solutions', 'Godrej & Boyce Mfg. Co. Ltd.', 'India', 'https://www.godrej.com', TRUE, 5, 1),
('Yale', 7, 'Security and locking solutions', 'Yale Security', 'USA', 'https://www.yale.com', TRUE, 5, 1),
('Dorma', 7, 'Door hardware solutions', 'Dorma Group', 'Germany', 'https://www.dorma.com', TRUE, 4, 1);

-- Add some comments for the table
ALTER TABLE brands COMMENT = 'Master table for brand information linked to product subcategories for construction materials and equipment';
