-- Create measurement_units table for construction company
-- This table will store various units of measurement used in construction

CREATE TABLE measurement_units (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  symbol VARCHAR(20) NOT NULL UNIQUE,
  description TEXT,
  category ENUM('length', 'area', 'volume', 'weight', 'quantity', 'time', 'other') NOT NULL DEFAULT 'other',
  base_unit_id INT NULL, -- Reference to base unit for conversion
  conversion_factor DECIMAL(15,6) NULL, -- Factor to convert to base unit
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_by INT NOT NULL,
  updated_by INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign key constraints
  CONSTRAINT fk_measurement_units_base_unit FOREIGN KEY (base_unit_id) REFERENCES measurement_units(id) ON DELETE SET NULL,
  CONSTRAINT fk_measurement_units_created_by FOREIGN KEY (created_by) REFERENCES users(id),
  CONSTRAINT fk_measurement_units_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
  
  -- Indexes for better performance
  INDEX idx_measurement_units_name (name),
  INDEX idx_measurement_units_symbol (symbol),
  INDEX idx_measurement_units_category (category),
  INDEX idx_measurement_units_active (is_active),
  INDEX idx_measurement_units_created_by (created_by)
);

-- Insert common measurement units used in construction
INSERT INTO measurement_units (name, symbol, description, category, created_by) VALUES
-- Length units
('Meter', 'm', 'Standard unit of length', 'length', 1),
('Centimeter', 'cm', 'One hundredth of a meter', 'length', 1),
('Millimeter', 'mm', 'One thousandth of a meter', 'length', 1),
('Kilometer', 'km', 'One thousand meters', 'length', 1),
('Inch', 'in', 'Imperial unit of length', 'length', 1),
('Foot', 'ft', 'Imperial unit of length, 12 inches', 'length', 1),
('Yard', 'yd', 'Imperial unit of length, 3 feet', 'length', 1),

-- Area units
('Square Meter', 'm²', 'Standard unit of area', 'area', 1),
('Square Centimeter', 'cm²', 'Area unit, square of centimeter', 'area', 1),
('Square Foot', 'sq ft', 'Imperial unit of area', 'area', 1),
('Square Yard', 'sq yd', 'Imperial unit of area', 'area', 1),
('Acre', 'acre', 'Unit of area, 4840 square yards', 'area', 1),
('Hectare', 'ha', 'Metric unit of area, 10000 square meters', 'area', 1),

-- Volume units
('Cubic Meter', 'm³', 'Standard unit of volume', 'volume', 1),
('Cubic Centimeter', 'cm³', 'Volume unit, cube of centimeter', 'volume', 1),
('Cubic Foot', 'cu ft', 'Imperial unit of volume', 'volume', 1),
('Liter', 'L', 'Metric unit of volume', 'volume', 1),
('Milliliter', 'mL', 'One thousandth of a liter', 'volume', 1),
('Gallon', 'gal', 'Imperial unit of volume', 'volume', 1),

-- Weight units
('Kilogram', 'kg', 'Standard unit of mass', 'weight', 1),
('Gram', 'g', 'One thousandth of a kilogram', 'weight', 1),
('Ton', 't', 'Metric ton, 1000 kilograms', 'weight', 1),
('Pound', 'lb', 'Imperial unit of weight', 'weight', 1),
('Ounce', 'oz', 'Imperial unit of weight', 'weight', 1),
('Quintal', 'q', 'Metric unit, 100 kilograms', 'weight', 1),

-- Quantity units
('Piece', 'pcs', 'Individual items or units', 'quantity', 1),
('Dozen', 'doz', 'Twelve pieces', 'quantity', 1),
('Hundred', '100s', 'One hundred pieces', 'quantity', 1),
('Thousand', '1000s', 'One thousand pieces', 'quantity', 1),
('Pair', 'pr', 'Two pieces together', 'quantity', 1),
('Set', 'set', 'Collection of related items', 'quantity', 1),
('Bundle', 'bdl', 'Group of items tied together', 'quantity', 1),
('Box', 'box', 'Container unit', 'quantity', 1),
('Bag', 'bag', 'Flexible container unit', 'quantity', 1),
('Carton', 'ctn', 'Cardboard container unit', 'quantity', 1),

-- Time units
('Hour', 'hr', 'Unit of time', 'time', 1),
('Day', 'day', 'Twenty-four hours', 'time', 1),
('Week', 'wk', 'Seven days', 'time', 1),
('Month', 'mo', 'Calendar month', 'time', 1),
('Year', 'yr', 'Twelve months', 'time', 1),

-- Construction specific units
('Running Meter', 'rm', 'Linear measurement for materials like pipes, cables', 'length', 1),
('Running Foot', 'rf', 'Linear measurement in imperial system', 'length', 1),
('Board Foot', 'bf', 'Volume unit for lumber', 'volume', 1),
('Cubic Yard', 'cu yd', 'Volume unit commonly used for concrete, soil', 'volume', 1),
('Square Yard', 'sq yd', 'Area unit for flooring, painting', 'area', 1);

-- Update conversion factors for common conversions (using meter as base for length)
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 1.0 WHERE symbol = 'm';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 0.01 WHERE symbol = 'cm';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 0.001 WHERE symbol = 'mm';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 1000.0 WHERE symbol = 'km';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 0.0254 WHERE symbol = 'in';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 0.3048 WHERE symbol = 'ft';
UPDATE measurement_units SET base_unit_id = 1, conversion_factor = 0.9144 WHERE symbol = 'yd';

-- Add some additional construction-specific units
INSERT INTO measurement_units (name, symbol, description, category, created_by) VALUES
('Brass', 'brass', 'Unit for measuring sand, stone (100 cubic feet)', 'volume', 1),
('CFT', 'cft', 'Cubic feet, commonly used in construction', 'volume', 1),
('SFT', 'sft', 'Square feet, commonly used for area measurement', 'area', 1),
('RFT', 'rft', 'Running feet, linear measurement', 'length', 1),
('Bigha', 'bigha', 'Traditional unit of area measurement', 'area', 1),
('Katha', 'katha', 'Traditional unit of area measurement', 'area', 1),
('Decimal', 'decimal', 'Traditional unit of area measurement', 'area', 1);
