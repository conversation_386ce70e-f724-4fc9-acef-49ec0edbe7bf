// src/routes/masters/index.js
const express = require('express');
const countryRoutes = require('./country.routes');
const stateRoutes = require('./state.routes');
const cityRoutes = require('./city.routes');
const localityRoutes = require('./locality.routes');
const bloodGroupRoutes = require('./bloodgroup.routes');
const employmentTypeRoutes = require('./employmenttype.routes');
const projectTypeRoutes = require('./projecttype.routes');
const qualificationRoutes = require('./qualification.routes');
const designationRoutes = require('./designation.routes');
const departmentRoutes = require('./department.routes');
const projectStatusRoutes = require('./projectstatus.routes');
const supplierTypeRoutes = require('./suppliertype.routes');
const productCategoryRoutes = require('./productcategory.routes');
const productSubcategoryRoutes = require('./productsubcategory.routes');
const gstValueRoutes = require('./gstvalue.routes');
// Import other master routes as they are created

const router = express.Router();

// Register all master routes
router.use('/countries', countryRoutes);
router.use('/states', stateRoutes);
router.use('/cities', cityRoutes);
router.use('/localities', localityRoutes);
router.use('/blood-groups', bloodGroupRoutes);
router.use('/employment-types', employmentTypeRoutes);
router.use('/project-types', projectTypeRoutes);
router.use('/qualifications', qualificationRoutes);  // Make sure this is included
router.use('/designations', designationRoutes);
router.use('/departments', departmentRoutes);
router.use('/project-statuses', projectStatusRoutes);  // Make sure this is included
router.use('/supplier-types', supplierTypeRoutes);
router.use('/product-categories', productCategoryRoutes);
router.use('/product-subcategories', productSubcategoryRoutes);
router.use('/gst-values', gstValueRoutes);
// Register other master routes as they are created

module.exports = router;